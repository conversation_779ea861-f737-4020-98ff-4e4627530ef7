using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;

namespace Scenes.Scripts
{
    /// <summary>
    /// Class to register player interaction in the game scene
    /// (click, drag, long click)
    /// </summary>
    public class PlayerInteractable : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON>ointer<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ointer<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IPointerClickHandler
    {
        [Header("Clicks")]
        public bool registerClick;
        public float clickTimeThreshold = 0.3f;
        public UnityEvent<PointerEventData> onClickEvents;

        [Header("Drags/Moves")]
        public bool registerMove;
        public float holdThreshold = 0.3f;
        public float dragDistanceThreshold = 5f;
        public UnityEvent<PointerEventData> onMoveStartEvents;
        public UnityEvent<PointerEventData> onMoveEvents;
        public UnityEvent<PointerEventData> onMoveEndEvents;

        [Header("Long Clicks")]
        public bool registerLongClick;
        public bool invokeWhenRelease;
        public float longClickTimeThreshold = 0.5f;
        public UnityEvent<PointerEventData> onLongClickEvents;

        private bool _isDown;
        private bool _shouldClick;
        private bool _shouldDrag;
        private bool _shouldLongClick;
        private float _downTime;
        private PointerEventData _downEvent;

        void Update()
        {
            if (_isDown)
            {
                _downTime += Time.deltaTime;

                if (registerLongClick && !_shouldLongClick && _downTime >= longClickTimeThreshold)
                {
                    if (!invokeWhenRelease)
                    {
                        EvokeLongClick(_downEvent);
                    }
                    _shouldLongClick = true;
                }

                if (registerClick && _shouldClick && _downTime >= clickTimeThreshold)
                {
                    _shouldClick = false;
                }

                if (registerMove && !_shouldDrag && _downTime >= holdThreshold)
                {
                    EvokeMoveStart(_downEvent);
                    _shouldDrag = true;
                }
            }
        }

        public void OnPointerDown(PointerEventData eventData)
        {
            _isDown = true;
            _shouldDrag = false;
            _shouldClick = true;
            _shouldLongClick = false;

            _downEvent = eventData;
            _downTime = 0f;
        }

        public void OnPointerUp(PointerEventData eventData)
        {
            if (_isDown)
            {
                if (registerClick && _shouldClick)
                {
                    EvokeClick(eventData);
                }

                if (registerMove && _shouldDrag)
                {
                    EvokeMoveEnd(eventData);
                }

                if (registerLongClick && invokeWhenRelease && _shouldLongClick)
                {
                    EvokeLongClick(eventData);
                }

                _shouldDrag = false;
                _shouldClick = false;
                _shouldLongClick = false;
                _isDown = false;

                _downEvent = null;
            }
        }

        public void OnPointerClick(PointerEventData eventData)
        {
            // Unity tự gọi sau PointerUp nếu có click hợp lệ
            // Bạn có thể bỏ trống hoặc dùng để debug
        }

        public void OnDrag(PointerEventData eventData)
        {
            if (_isDown && registerMove)
            {
                if (!_shouldDrag)
                {
                    if (Mathf.Abs(eventData.delta.x) > dragDistanceThreshold ||
                        Mathf.Abs(eventData.delta.y) > dragDistanceThreshold)
                    {
                        _shouldDrag = true;
                        EvokeMoveStart(eventData);
                    }
                }

                if (_shouldDrag)
                {
                    EvokeMove(eventData);
                }
            }
        }

        // ==== Event Invoke Methods ====

        private void EvokeClick(PointerEventData eventData)
        {
            if (registerClick)
            {
                Debug.Log("Clicked " + gameObject.name);
                onClickEvents?.Invoke(eventData);
            }
        }

        private void EvokeMoveStart(PointerEventData eventData)
        {
            if (registerMove)
            {
                Debug.Log("Move started " + gameObject.name);
                onMoveStartEvents?.Invoke(eventData);
            }
        }

        private void EvokeMove(PointerEventData eventData)
        {
            if (registerMove)
            {
                Debug.Log("Moving " + gameObject.name);
                onMoveEvents?.Invoke(eventData);
            }
        }

        private void EvokeMoveEnd(PointerEventData eventData)
        {
            if (registerMove)
            {
                Debug.Log("Move ended " + gameObject.name);
                onMoveEndEvents?.Invoke(eventData);
            }
        }

        private void EvokeLongClick(PointerEventData eventData)
        {
            if (registerLongClick)
            {
                Debug.Log("Long Clicked " + gameObject.name);
                onLongClickEvents?.Invoke(eventData);
            }
        }
    }
}
