using UnityEngine;
using System.Collections.Generic;

// create asset menu
[CreateAssetMenu(fileName = "CommonShelve", menuName = "Game/CommonShelve")]
public class CommonShelve : MonoBehaviour
{
    //create private array of gameobjects serializable
    [SerializeField] private GameObject[] gameObjects;

    public bool IsEmpty
    {
        get
        {
            for (int i = 0; i < gameObjects.Length; i++)
            {
                var layer = gameObjects[i];
                if (layer.transform.childCount > 0)
                {
                    return false;
                }
            }
            return true;
        }
    }
    
    // create private dictionary int,int named layer0GoodsMap
    private Dictionary<int, int> _layer0GoodsMap = new Dictionary<int, int>();

    // onLoad
    private void Awake()
    {
        for (int i = 0; i < gameObjects.Length; i++)
        {
            _layer0GoodsMap.Add(i, 0);
        }
    }
}
